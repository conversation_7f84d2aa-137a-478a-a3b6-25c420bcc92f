jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/prepare
      - run: pnpm build
      - run: node lib/index.js
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/prepare
      - run: pnpm lint
  prettier:
    name: Prettier
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/prepare
      - run: pnpm format --list-different
  type_check:
    name: Type Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/prepare
      - run: pnpm tsc


name: CI


on:
  pull_request: ~
  push:
    branches:
      - main
